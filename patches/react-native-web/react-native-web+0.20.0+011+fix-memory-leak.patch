diff --git a/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedProps.js b/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedProps.js
index 9ba3336..2d67005 100644
--- a/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedProps.js
+++ b/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedProps.js
@@ -68,6 +68,7 @@ class AnimatedProps extends AnimatedNode {
     if (this.__isNative && this._animatedView) {
       this.__disconnectAnimatedView();
     }
+    this._animatedView = null
     for (var key in this._props) {
       var value = this._props[key];
       if (value instanceof AnimatedNode) {
