name: PR Author Checklist

on:
  # We use pull_request_target here so that the GitHub token will have read/write access and be able to modify the PR description if needed.
  # Warning: – when using the pull_request_target event, DO NOT checkout code from an untrusted branch: https://securitylab.github.com/research/github-actions-preventing-pwn-requests/
  pull_request_target:
    types: [opened, edited, reopened, synchronize]
    branches: [main]
    paths-ignore: ['docs/articles/**/*.md', 'docs/redirects.csv', 'docs/assets/images/**']

jobs:
  # Note: P<PERSON> specifically looks for the name of this job, "checklist", so if the name of the job is changed,
  # then you also need to go into PHP and update the name of this job in the GH_JOB_NAME_CHECKLIST constant
  checklist:
    runs-on: ubuntu-latest
    if: github.actor != 'OSBotify' && github.actor != 'imgbot[bot]'
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: authorChecklist.ts
        uses: ./.github/actions/javascript/authorChecklist
        with:
          GITHUB_TOKEN: ${{ github.token }}
