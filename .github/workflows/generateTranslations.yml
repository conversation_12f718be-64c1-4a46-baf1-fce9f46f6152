name: Generate static translations

on:
  pull_request:
    types: [opened, synchronize]
    paths-ignore: ['docs/articles/**/*.md', 'docs/redirects.csv', 'docs/assets/images/**']

jobs:
  # We always run dry-run the script to verify that it still works.
  # The generateTranslations script runs with ts-node, which can't handle Flow
  # (the specialized JS that React Native is written in).
  # Therefore, adding an import in the wrong place could break the script, even if you didn't modify the script.
  dryRun:
    runs-on: ubuntu-latest
    steps:
      # v4
      - name: Checkout
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Run generateTranslations dry run
        run: npx ts-node ./scripts/generateTranslations.ts --dry-run

      - name: Explain failure
        if: failure()
        run: |
          echo '::error:: 😦 Something you did broke scripts/generateTranslations.ts. Most likely, this means you added an import that caused react-native to be directly or indirectly imported into the script.'
          exit 1
